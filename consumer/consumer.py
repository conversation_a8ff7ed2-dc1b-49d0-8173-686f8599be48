from __future__ import annotations
import os, time, json as stdjson
from typing import <PERSON><PERSON>, Dict, Any
import paho.mqtt.client as mqtt
import ujson as json
import redis

from common.validators import validate_payload_dict, DiscardMessage

MQTT_HOST = os.getenv("MQTT_HOST", "localhost")
MQTT_PORT = int(os.getenv("MQTT_PORT", "1883"))
MQTT_TOPIC = os.getenv("MQTT_TOPIC", "device/messages/#")

# Redis configuration
REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
REDIS_DB = int(os.getenv("REDIS_DB", "0"))
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", None)

COUNTERS = {"received": 0, "ok": 0, "discarded": 0, "errors": 0, "redis_saved": 0, "redis_errors": 0}
REPORT_EVERY = int(os.getenv("REPORT_EVERY", "5"))

# Initialize Redis client
redis_client = None
try:
    redis_client = redis.Redis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        db=REDIS_DB,
        password=REDIS_PASSWORD,
        decode_responses=True,
        socket_connect_timeout=5,
        socket_timeout=5
    )
    # Test connection
    redis_client.ping()
    print(f"[consumer] Redis connected to {REDIS_HOST}:{REDIS_PORT}")
except Exception as e:
    print(f"[consumer] Redis connection failed: {e}")
    redis_client = None

# If using real protobuf, swap this for ParseFromString

def unpack_payload(payload: bytes) -> Tuple[str, str, Dict[str, Any]]:
    try:
        d = json.loads(payload)
        messageType = d.get("messageType", "")
        messageVersion = d.get("messageVersion", "")
        message_str = d.get("message", "{}")
        payload_dict = stdjson.loads(message_str)
        return messageType, messageVersion, payload_dict
    except Exception as e:
        raise DiscardMessage(f"bad envelope: {e}")


def on_connect(client, userdata, flags, reason_code, properties):
    print(f"[consumer] connected rc={reason_code}")
    client.subscribe(MQTT_TOPIC, qos=0)


_last_report = time.time()

def on_message(client, userdata, msg):
    global _last_report
    COUNTERS["received"] += 1

    try:
        message_type, message_version, payload_dict = unpack_payload(msg.payload)
        # schema/business validation
        payload_checked = validate_payload_dict(payload_dict)

        # process the OK message
        # Save to Redis if available
        if redis_client:
            try:
                # Create a unique key for each device message
                device_id = payload_checked['deviceId']
                seq = payload_checked['seq']
                timestamp = payload_checked['ts']

                # Use a key pattern: device:{deviceId}:{seq}:{timestamp}
                redis_key = f"device:{device_id}:{seq}:{timestamp}"

                # Save the complete payload as JSON
                redis_client.setex(
                    redis_key,
                    3600,  # TTL: 1 hour (configurable via env var)
                    json.dumps(payload_checked)
                )

                # Also maintain a sorted set for each device for easy querying
                device_key = f"device_messages:{device_id}"
                redis_client.zadd(device_key, {redis_key: timestamp})
                redis_client.expire(device_key, 3600)  # TTL for the sorted set

                COUNTERS["redis_saved"] += 1

            except Exception as redis_error:
                COUNTERS["redis_errors"] += 1
                # Optionally log Redis errors
                # print(f"Redis save error: {redis_error}")

        COUNTERS["ok"] += 1

    except DiscardMessage as e:
        COUNTERS["discarded"] += 1
        # Optionally log e quietly, or route to DLQ/topic
        # print(f"discarded: {e}")
    except Exception as e:
        COUNTERS["errors"] += 1
        # print(f"error: {e}")

    # lightweight periodic report
    if time.time() - _last_report >= REPORT_EVERY:
        redis_status = f"redis_saved={COUNTERS['redis_saved']} redis_errors={COUNTERS['redis_errors']}" if redis_client else "redis=disabled"
        print(
            f"[consumer] received={COUNTERS['received']} ok={COUNTERS['ok']} "
            f"discarded={COUNTERS['discarded']} errors={COUNTERS['errors']} {redis_status}"
        )
        _last_report = time.time()


def main():
    client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION2, client_id="consumer-1")
    client.on_connect = on_connect
    client.on_message = on_message

    client.connect(MQTT_HOST, MQTT_PORT, keepalive=60)
    client.loop_forever(retry_first_connection=True)

if __name__ == "__main__":
    main()
