from __future__ import annotations
import time, json, ujson as fastjson
from dataclasses import dataclass, asdict
from typing import Any, Dict

# If you use protobuf, replace this with your generated consumer_pb2.GenericMessageEnvelope
# and call ParseFromString in consumer.unpack_payload.

@dataclass
class GenericMessageEnvelope:
    messageType: str
    messageVersion: str
    message: str  # JSON string payload for simplicity here
    sentAt: int

    def SerializeToString(self) -> bytes:
        return fastjson.dumps(asdict(self)).encode("utf-8")

    @staticmethod
    def FromDict(d: Dict[str, Any]) -> "GenericMessageEnvelope":
        return GenericMessageEnvelope(
            messageType=d.get("messageType", "DeviceStatus"),
            messageVersion=d.get("messageVersion", "1.0"),
            message=d.get("message", "{}"),
            sentAt=d.get("sentAt", int(time.time())),
        )
