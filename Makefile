.PHONY: up down sim log-consumer stats redis-cli redis-stats

up:
	docker compose up -d --build

down:
	docker compose down -v

sim:
	python -m simulator.simulator \
	  --broker localhost --port 1883 \
	  --devices 500 --rate 3 --burst 1 \
	  --invalid-rate 0.01

log-consumer:
	docker compose logs -f consumer

stats:
	docker stats --no-stream

redis-cli:
	docker compose exec redis redis-cli

redis-stats:
	python redis_query.py --stats
