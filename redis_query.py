#!/usr/bin/env python3
"""
Redis Query Utility for MQTT Device Messages

This script provides utilities to query and analyze device messages stored in Redis.
"""

import os
import redis
import json
import argparse
from typing import List, Dict, Any

# Redis configuration
REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
REDIS_DB = int(os.getenv("REDIS_DB", "0"))
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", None)

def get_redis_client():
    """Initialize and return Redis client."""
    try:
        client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_DB,
            password=REDIS_PASSWORD,
            decode_responses=True
        )
        client.ping()
        return client
    except Exception as e:
        print(f"Redis connection failed: {e}")
        return None

def list_devices(client: redis.Redis) -> List[str]:
    """List all devices that have messages stored."""
    device_keys = client.keys("device_messages:*")
    devices = [key.replace("device_messages:", "") for key in device_keys]
    return sorted(devices)

def get_device_messages(client: redis.Redis, device_id: str, limit: int = 10) -> List[Dict[str, Any]]:
    """Get recent messages for a specific device."""
    device_key = f"device_messages:{device_id}"
    
    # Get message keys sorted by timestamp (most recent first)
    message_keys = client.zrevrange(device_key, 0, limit - 1)
    
    messages = []
    for key in message_keys:
        try:
            message_data = client.get(key)
            if message_data:
                messages.append(json.loads(message_data))
        except Exception as e:
            print(f"Error reading message {key}: {e}")
    
    return messages

def get_stats(client: redis.Redis) -> Dict[str, Any]:
    """Get overall statistics about stored messages."""
    device_keys = client.keys("device_messages:*")
    total_devices = len(device_keys)
    
    total_messages = 0
    for device_key in device_keys:
        total_messages += client.zcard(device_key)
    
    # Get memory usage
    memory_info = client.info("memory")
    
    return {
        "total_devices": total_devices,
        "total_messages": total_messages,
        "memory_used_mb": round(memory_info.get("used_memory", 0) / 1024 / 1024, 2),
        "redis_version": client.info("server").get("redis_version", "unknown")
    }

def main():
    parser = argparse.ArgumentParser(description="Query MQTT device messages from Redis")
    parser.add_argument("--list-devices", action="store_true", help="List all devices")
    parser.add_argument("--device", type=str, help="Get messages for specific device")
    parser.add_argument("--limit", type=int, default=10, help="Limit number of messages (default: 10)")
    parser.add_argument("--stats", action="store_true", help="Show overall statistics")
    
    args = parser.parse_args()
    
    client = get_redis_client()
    if not client:
        return 1
    
    if args.list_devices:
        devices = list_devices(client)
        print(f"Found {len(devices)} devices:")
        for device in devices:
            print(f"  - {device}")
    
    elif args.device:
        messages = get_device_messages(client, args.device, args.limit)
        print(f"Recent {len(messages)} messages for device '{args.device}':")
        for i, msg in enumerate(messages, 1):
            print(f"  {i}. seq={msg.get('seq')} status={msg.get('status')} ts={msg.get('ts')}")
            if len(str(msg)) < 200:  # Show full message if short
                print(f"     {msg}")
    
    elif args.stats:
        stats = get_stats(client)
        print("Redis Statistics:")
        print(f"  Total devices: {stats['total_devices']}")
        print(f"  Total messages: {stats['total_messages']}")
        print(f"  Memory used: {stats['memory_used_mb']} MB")
        print(f"  Redis version: {stats['redis_version']}")
    
    else:
        parser.print_help()
    
    return 0

if __name__ == "__main__":
    exit(main())
