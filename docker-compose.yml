version: "3.9"
services:
  mosquitto:
    image: eclipse-mosquitto:2
    ports: ["1883:1883", "9001:9001"]
    volumes:
      - ./mosquitto.conf:/mosquitto/config/mosquitto.conf:ro
    healthcheck:
      test: ["CMD", "mosquitto_pub", "-h", "localhost", "-t", "health", "-m", "ok"]
      interval: 10s
      timeout: 3s
      retries: 5

  consumer:
    build: .
    image: device-consumer:latest
    command: python -m consumer.consumer
    environment:
      - MQTT_HOST=mosquitto
      - MQTT_PORT=1883
      - MQTT_TOPIC=device/messages/#
      - LOG_LEVEL=INFO
    depends_on:
      - mosquitto
