# Quickstart

1) `pip install -r requirements.txt`
2) `docker compose up -d`  (spins mosquitto + redis + consumer)
3) In another terminal run the simulator:

```bash
python -m simulator.simulator --broker localhost --port 1883 \
  --devices 500 --rate 3 --invalid-rate 0.01
```

You'll see both **sim** and **consumer** print counters every ~5s.

## Redis Integration

The consumer now automatically saves all validated device messages to Redis with the following features:

- **Message Storage**: Each message is stored with a unique key pattern: `device:{deviceId}:{seq}:{timestamp}`
- **Device Indexing**: Messages are indexed by device in sorted sets for easy querying
- **TTL**: Messages expire after 1 hour (configurable)
- **Error Handling**: Redis failures don't stop message processing

### Querying Redis Data

Use the included `redis_query.py` utility:

```bash
# List all devices with stored messages
python redis_query.py --list-devices

# Get recent messages for a specific device
python redis_query.py --device dev-abc123 --limit 20

# Show overall statistics
python redis_query.py --stats
```

### Redis Configuration

Environment variables for Redis connection:
- `REDIS_HOST` (default: localhost)
- `REDIS_PORT` (default: 6379)
- `REDIS_DB` (default: 0)
- `REDIS_PASSWORD` (optional)

## Notes
- To switch to real protobuf, replace `common/envelope.py` with your generated `consumer_pb2` and move the `ParseFromString(payload)` into `unpack_payload()`.
- The `DiscardMessage` exception is intentional so your handler can drop invalid messages without noisy tracebacks.
- Redis integration is optional - if Redis is unavailable, the consumer continues processing without saving to Redis.
- For persistent metrics without Prometheus, redirect stdout to a file and parse later, or wrap counters with a tiny HTTP endpoint exposing JSON for your external poller.
