# Quickstart

1) `pip install -r requirements.txt`
2) `docker compose up -d`  (spins mosquitto + consumer)
3) In another terminal run the simulator:

```bash
python -m simulator.simulator --broker localhost --port 1883 \
  --devices 500 --rate 3 --invalid-rate 0.01
```

You'll see both **sim** and **consumer** print counters every ~5s.

## Notes
- To switch to real protobuf, replace `common/envelope.py` with your generated `consumer_pb2` and move the `ParseFromString(payload)` into `unpack_payload()`.
- The `DiscardMessage` exception is intentional so your handler can drop invalid messages without noisy tracebacks.
- For persistent metrics without Prometheus, redirect stdout to a file and parse later, or wrap counters with a tiny HTTP endpoint exposing JSON for your external poller.
